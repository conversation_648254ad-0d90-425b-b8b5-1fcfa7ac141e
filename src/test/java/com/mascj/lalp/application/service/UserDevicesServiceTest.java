package com.mascj.lalp.application.service;

import com.mascj.lalp.common.context.TenantContext;
import com.mascj.lalp.domain.model.*;
import com.mascj.lalp.interfaces.rest.backend.dto.DeviceInfo;
import com.mascj.lalp.interfaces.rest.backend.dto.DeviceStatistics;
import com.mascj.lalp.interfaces.rest.backend.dto.UserDevicesResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

/**
 * UserDevicesService 测试类
 */
@ExtendWith(MockitoExtension.class)
class UserDevicesServiceTest {

    @Mock
    private WarehouseService warehouseService;

    @Mock
    private DroneService droneService;

    @Mock
    private InventoryService inventoryService;

    @InjectMocks
    private UserDevicesService userDevicesService;

    private Long testTenantId = 1L;
    private List<Warehouse> testWarehouses;
    private List<Drone> testDrones;
    private List<Inventory> testInventories;

    @BeforeEach
    void setUp() {
        // 设置租户上下文
        TenantContext.setTenantId(testTenantId);

        // 创建测试数据
        testWarehouses = createTestWarehouses();
        testDrones = createTestDrones();
        testInventories = createTestInventories();
    }

    @Test
    void testGetCurrentUserDevices() {
        // 模拟Service调用
        when(warehouseService.getWarehousesByTenantId(testTenantId)).thenReturn(testWarehouses);
        when(droneService.getDronesByTenantId(testTenantId)).thenReturn(testDrones);
        when(inventoryService.getInventoriesByWarehouseIds(anyList())).thenReturn(testInventories);

        // 执行测试
        UserDevicesResponse response = userDevicesService.getCurrentUserDevices();

        // 验证结果
        assertNotNull(response);
        assertEquals(2, response.getWarehouses().size());
        assertEquals(2, response.getDrones().size());
        assertEquals(1, response.getInventories().size());

        // 验证统一设备列表
        List<DeviceInfo> allDevices = response.getAllDevices();
        assertNotNull(allDevices);
        assertEquals(5, allDevices.size()); // 2仓库 + 2无人机 + 1库存

        // 验证设备类型
        long warehouseCount = allDevices.stream()
                .filter(device -> device.getDeviceType() == DeviceType.WAREHOUSE)
                .count();
        long droneCount = allDevices.stream()
                .filter(device -> device.getDeviceType() == DeviceType.DRONE)
                .count();
        long inventoryCount = allDevices.stream()
                .filter(device -> device.getDeviceType() == DeviceType.INVENTORY)
                .count();

        assertEquals(2, warehouseCount);
        assertEquals(2, droneCount);
        assertEquals(1, inventoryCount);

        // 验证统计信息
        DeviceStatistics statistics = response.getStatistics();
        assertNotNull(statistics);
        assertEquals(2, statistics.getTotalWarehouses());
        assertEquals(2, statistics.getTotalDrones());
        assertEquals(1, statistics.getTotalInventories());
        assertEquals(5, statistics.getTotalDevices());
    }

    private List<Warehouse> createTestWarehouses() {
        Warehouse warehouse1 = new Warehouse();
        warehouse1.setId(1L);
        warehouse1.setName("测试仓库1");
        warehouse1.setCode("WH001");
        warehouse1.setStatus(WarehouseStatus.ONLINE);
        warehouse1.setTenantId(testTenantId);
        warehouse1.setCreateTime(LocalDateTime.now());

        Warehouse warehouse2 = new Warehouse();
        warehouse2.setId(2L);
        warehouse2.setName("测试仓库2");
        warehouse2.setCode("WH002");
        warehouse2.setStatus(WarehouseStatus.OFFLINE);
        warehouse2.setTenantId(testTenantId);
        warehouse2.setCreateTime(LocalDateTime.now());

        return Arrays.asList(warehouse1, warehouse2);
    }

    private List<Drone> createTestDrones() {
        Drone drone1 = new Drone();
        drone1.setId(1L);
        drone1.setName("测试无人机1");
        drone1.setDroneId("DR001");
        drone1.setStatus("ONLINE");
        drone1.setTenantId(testTenantId);

        Drone drone2 = new Drone();
        drone2.setId(2L);
        drone2.setName("测试无人机2");
        drone2.setDroneId("DR002");
        drone2.setStatus("OFFLINE");
        drone2.setTenantId(testTenantId);

        return Arrays.asList(drone1, drone2);
    }

    private List<Inventory> createTestInventories() {
        Inventory inventory = new Inventory();
        inventory.setId(1L);
        inventory.setCode("INV001");
        inventory.setWarehouseName("测试仓库1");
        inventory.setWarehouseId(1L);
        inventory.setCreateTime(LocalDateTime.now());
        inventory.setCheckTime(LocalDateTime.now());

        return Arrays.asList(inventory);
    }
}
