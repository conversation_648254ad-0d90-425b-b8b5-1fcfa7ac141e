package com.mascj.lalp.cabin.controller;

import com.mascj.lalp.cabin.api.*;
import com.mascj.lalp.cabin.dto.DeviceStatusResponse;
import com.mascj.lalp.cabin.dto.DeviceWarningsResponse;
import com.mascj.lalp.cabin.dto.DroneApproachingResponse;

import com.mascj.lalp.cabin.service.DroneApproachingService;
import com.mascj.lalp.cabin.service.DroneCabinService;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 无人机货仓控制接口
 */
@Slf4j
@Tag(name = "无人机货仓控制", description = "控制无人机货仓开关和状态查询")
@RestController
@RequestMapping("/api/drone")
@RequiredArgsConstructor
public class DroneCabinController {

    private final DroneCabinService droneCabinService;
    private final DroneApproachingService droneApproachingService;

    @Operation(summary = "无人机即将到达通知",
               description = "无人机即将到达时调用此接口，根据无人机SN号查找配送任务，然后打开对应物流货仓的货舱盖")
    @PostMapping("/{droneSn}/approaching")
    public ApiResult<DroneApproachingResponse> droneApproaching(
            @Parameter(description = "无人机SN号", required = true, example = "SN001")
            @PathVariable String droneSn,
            @Parameter(description = "任务ID（可选）", example = "123")
            @RequestParam(required = false) String taskId) {

        // 调用服务处理无人机即将到达逻辑
        DroneApproachingService.DroneApproachingResult result =
                droneApproachingService.handleDroneApproaching(droneSn, taskId);

        // 使用实体类的静态方法创建响应
        return DroneApproachingResponse.fromResult(result, droneSn, taskId);
    }

    // ========== 统一控制接口 ==========

    /**
     * 统一的设备控制接口
     * @param deviceId 设备ID
     * @param action 动作类型
     * @param actionCode 动作代码
     * @param withStatus 是否返回状态
     * @return 控制结果
     */
    @Operation(summary = "统一设备控制", description = "统一的设备控制接口，支持所有控制指令")
    @PostMapping("/{deviceId}/control")
    public ResponseEntity<Map<String, Object>> controlDevice(
            @Parameter(description = "设备ID", required = true, example = "49004A001151323532363931")
            @PathVariable String deviceId,
            @Parameter(description = "动作类型", required = true, example = "cover")
            @RequestParam String action,
            @Parameter(description = "动作代码", required = true, example = "open")
            @RequestParam String actionCode,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {

        log.info("设备控制: deviceId={}, action={}, actionCode={}, withStatus={}",
                deviceId, action, actionCode, withStatus);

        try {
            DroneCommand command = createCommand(action, actionCode);

            // 发送控制指令
            droneCabinService.sendControlCommand(deviceId, command);

            Map<String, Object> result = Map.of(
                "success", true,
                "message", "指令发送成功",
                "deviceId", deviceId,
                "action", action + "_" + actionCode,
                "timestamp", System.currentTimeMillis()
            );

            if (withStatus) {
                // 等待一小段时间让设备处理指令
                try {
                    Thread.sleep(500);
                    // 获取设备状态
                    DeviceStatusResponse status = droneCabinService.getDeviceStatus(deviceId);
                    result = Map.of(
                        "success", true,
                        "message", "指令发送成功",
                        "deviceId", deviceId,
                        "action", action + "_" + actionCode,
                        "status", status,
                        "timestamp", System.currentTimeMillis()
                    );
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("状态检查被中断: deviceId={}", deviceId);
                } catch (Exception e) {
                    log.warn("获取设备状态失败: deviceId={}", deviceId, e);
                }
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("设备控制失败: deviceId={}, action={}, actionCode={}",
                    deviceId, action, actionCode, e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "控制失败: " + e.getMessage(),
                "deviceId", deviceId,
                "action", action + "_" + actionCode,
                "timestamp", System.currentTimeMillis()
            ));
        }
    }

    // ========== 便捷接口（基于统一接口） ==========

    @Operation(summary = "打开货仓盖", description = "打开指定设备的货仓盖")
    @PostMapping("/{deviceId}/cover/open")
    public ResponseEntity<Map<String, Object>> openCover(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "cover", "open", withStatus);
    }

    @Operation(summary = "关闭货仓盖", description = "关闭指定设备的货仓盖")
    @PostMapping("/{deviceId}/cover/close")
    public ResponseEntity<Map<String, Object>> closeCover(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "cover", "close", withStatus);
    }

    @Operation(summary = "推动推杆", description = "推动无人机的推杆")
    @PostMapping("/{deviceId}/lever/push")
    public ResponseEntity<Map<String, Object>> pushLever(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "lever", "push", withStatus);
    }

    @Operation(summary = "重置推杆", description = "重置无人机的推杆位置")
    @PostMapping("/{deviceId}/lever/reset")
    public ResponseEntity<Map<String, Object>> resetLever(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "lever", "reset", withStatus);
    }

    @Operation(summary = "打开取货口", description = "打开指定设备的取货口")
    @PostMapping("/{deviceId}/pickup/open")
    public ResponseEntity<Map<String, Object>> openPickup(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "pickup", "open", withStatus);
    }

    @Operation(summary = "关闭取货口", description = "关闭指定设备的取货口")
    @PostMapping("/{deviceId}/pickup/close")
    public ResponseEntity<Map<String, Object>> closePickup(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "pickup", "close", withStatus);
    }

    @Operation(summary = "打开送货口", description = "打开指定设备的送货口")
    @PostMapping("/{deviceId}/delivery/open")
    public ResponseEntity<Map<String, Object>> openDelivery(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "delivery", "open", withStatus);
    }

    @Operation(summary = "关闭送货口", description = "关闭指定设备的送货口")
    @PostMapping("/{deviceId}/delivery/close")
    public ResponseEntity<Map<String, Object>> closeDelivery(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "delivery", "close", withStatus);
    }

    @Operation(summary = "读取电子秤重量", description = "读取指定设备的电子秤重量")
    @PostMapping("/{deviceId}/scale/read")
    public ResponseEntity<Map<String, Object>> readScale(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "是否返回状态", example = "true")
            @RequestParam(defaultValue = "true") boolean withStatus) {
        return controlDevice(deviceId, "scale", "read", withStatus);
    }

    // ========== 特殊控制接口（有额外参数的保留原样） ==========

    @Operation(summary = "启动视频流", description = "启动指定设备的视频流")
    @PostMapping("/{deviceId}/video/start")
    public ResponseEntity<Map<String, Object>> startVideoStream(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "视频流编号", required = true, example = "1")
            @RequestParam int streamId,
            @Parameter(description = "RTSP流地址", required = true, example = "rtsp://example.com/stream1")
            @RequestParam String rtspUrl) {

        log.info("启动视频流: deviceId={}, streamId={}, rtspUrl={}", deviceId, streamId, rtspUrl);

        try {
            droneCabinService.sendControlCommand(deviceId, VideoStreamCommand.startStream(streamId, rtspUrl));
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "视频流启动指令发送成功",
                "deviceId", deviceId,
                "streamId", streamId,
                "rtspUrl", rtspUrl,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("启动视频流失败: deviceId={}, streamId={}", deviceId, streamId, e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "启动视频流失败: " + e.getMessage(),
                "deviceId", deviceId,
                "streamId", streamId,
                "timestamp", System.currentTimeMillis()
            ));
        }
    }

    @Operation(summary = "停止视频流", description = "停止指定设备的视频流")
    @PostMapping("/{deviceId}/video/stop")
    public ResponseEntity<Map<String, Object>> stopVideoStream(
            @Parameter(description = "设备ID", required = true)
            @PathVariable String deviceId,
            @Parameter(description = "视频流编号", required = true, example = "1")
            @RequestParam int streamId) {

        log.info("停止视频流: deviceId={}, streamId={}", deviceId, streamId);

        try {
            droneCabinService.sendControlCommand(deviceId, VideoStreamCommand.stopStream(streamId));
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "视频流停止指令发送成功",
                "deviceId", deviceId,
                "streamId", streamId,
                "timestamp", System.currentTimeMillis()
            ));
        } catch (Exception e) {
            log.error("停止视频流失败: deviceId={}, streamId={}", deviceId, streamId, e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "停止视频流失败: " + e.getMessage(),
                "deviceId", deviceId,
                "streamId", streamId,
                "timestamp", System.currentTimeMillis()
            ));
        }
    }

    @Operation(summary = "升降台下降到取货口位", description = "发送指令让升降台下降到取货口位")
    @PostMapping("/{deviceId}/platform/lower-to-pickup")
    public ResponseEntity<Void> lowerPlatformToPickup(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, LiftPlatformCommand.lowerToPickup());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "升降台下降到送货口位", description = "发送指令让升降台下降到送货口位")
    @PostMapping("/{deviceId}/platform/lower-to-delivery")
    public ResponseEntity<Void> lowerPlatformToDelivery(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, LiftPlatformCommand.lowerToDelivery());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "升降台升到停机坪位", description = "发送指令让升降台升到停机坪位")
    @PostMapping("/{deviceId}/platform/rise-to-landing")
    public ResponseEntity<Void> risePlatformToLanding(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, LiftPlatformCommand.riseToLanding());
        return ResponseEntity.ok().build();
    }

    // ========== 货仓推杆控制 (action=108) ==========
    @Operation(summary = "推动货仓推杆", description = "发送指令推动货仓推杆")
    @PostMapping("/{deviceId}/cargo-pusher/push")
    public ResponseEntity<Void> pushCargoPusher(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, CargoPusherCommand.push());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "收回货仓推杆", description = "发送指令收回货仓推杆")
    @PostMapping("/{deviceId}/cargo-pusher/retract")
    public ResponseEntity<Void> retractCargoPusher(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, CargoPusherCommand.retract());
        return ResponseEntity.ok().build();
    }

    // ========== 上报周期设置 (action=109) ==========
    @Operation(summary = "设置状态上报周期", description = "设置无人机状态上报周期")
    @PostMapping("/{deviceId}/report-interval")
    public ResponseEntity<Void> setReportInterval(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId,
            @Parameter(description = "上报周期(秒)", required = true, example = "60")
            @RequestParam int duration) throws Exception {
        droneCabinService.sendControlCommand(deviceId, ReportIntervalCommand.setInterval(duration));
        return ResponseEntity.ok().build();
    }

    // ========== 固件更新 (action=110) ==========
    @Operation(summary = "固件更新", description = "发送固件更新指令")
    @PostMapping("/{deviceId}/firmware/update")
    public ResponseEntity<Void> updateFirmware(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId,
            @Parameter(description = "固件下载地址", required = true)
            @RequestParam String url,
            @Parameter(description = "固件版本", required = true)
            @RequestParam String version,
            @Parameter(description = "CRC32校验码", required = true)
            @RequestParam String checksum) throws Exception {
        droneCabinService.sendControlCommand(deviceId, FirmwareUpdateCommand.update(url, version, checksum));
        return ResponseEntity.ok().build();
    }

    // ========== 批量控制 (action=111) ==========
    @Operation(summary = "批量控制（协议标准格式）", description = "发送协议标准格式的批量控制指令")
    @PostMapping("/{deviceId}/batch/protocol")
    public ResponseEntity<Map<String, Object>> protocolBatchControl(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId,
            @RequestBody BatchControlCommand batchCommand) throws Exception {

        droneCabinService.sendControlCommand(deviceId, batchCommand);

        int commandCount = 0;
        if (batchCommand.getData() instanceof List) {
            commandCount = ((List<?>) batchCommand.getData()).size();
        }

        Map<String, Object> response = Map.of(
                "success", true,
                "message", "协议标准格式批量控制指令发送成功",
                "deviceId", deviceId,
                "commandCount", commandCount,
                "format", "protocol",
                "timestamp", System.currentTimeMillis()
        );

        return ResponseEntity.ok(response);
    }

    @Operation(summary = "批量控制（扩展格式）", description = "发送扩展格式的批量控制指令（向后兼容）")
    @PostMapping("/{deviceId}/batch")
    public ResponseEntity<Void> batchControl(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId,
            @RequestBody BatchControlCommand batchCommand) throws Exception {
        droneCabinService.sendControlCommand(deviceId, batchCommand);
        return ResponseEntity.ok().build();
    }

    // ========== 状态查询 ==========
    @Operation(summary = "获取状态", description = "获取无人机的当前状态")
    @GetMapping("/{deviceId}/status")
    public ResponseEntity<DeviceStatusResponse> getStatus(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) {
        DeviceStatusResponse status = droneCabinService.getDeviceStatus(deviceId);
        return ResponseEntity.ok(status);
    }

    // ========== 告警查询 ==========
    @Operation(summary = "获取告警信息", description = "获取无人机的告警信息")
    @GetMapping("/{deviceId}/warnings")
    public ResponseEntity<DeviceWarningsResponse> getWarnings(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) {
        DeviceWarningsResponse warnings = droneCabinService.getDeviceWarnings(deviceId);
        return ResponseEntity.ok(warnings);
    }

    // ========== 设备管理 ==========
    @Operation(summary = "获取在线设备列表", description = "获取当前在线的无人机设备列表")
    @GetMapping("/online")
    public ResponseEntity<List<String>> getOnlineDevices() {
        List<String> devices = droneCabinService.getOnlineDevices();
        return ResponseEntity.ok(devices);
    }

    @Operation(summary = "清除设备缓存", description = "清除指定设备的状态和告警缓存")
    @DeleteMapping("/{deviceId}/cache")
    public ResponseEntity<Void> clearDeviceCache(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) {
        droneCabinService.clearDeviceCache(deviceId);
        return ResponseEntity.ok().build();
    }

    // ========== 私有工具方法 ==========

    /**
     * 根据动作类型和代码创建对应的命令对象（根据MQTT协议文档）
     */
    private DroneCommand createCommand(String action, String actionCode) {
        switch (action.toLowerCase()) {
            case "cover":
            case "101":
                return "1".equals(actionCode) || "open".equals(actionCode) ?
                    CabinCoverCommand.open() : CabinCoverCommand.close();
            case "lever":
            case "102":
                return "1".equals(actionCode) || "push".equals(actionCode) ?
                    DroneLeverCommand.push() : DroneLeverCommand.reset();
            case "pickup":
            case "103":
                return "1".equals(actionCode) || "open".equals(actionCode) ?
                    CargoPickupCommand.open() : CargoPickupCommand.close();
            case "delivery":
            case "104":
                return "1".equals(actionCode) || "open".equals(actionCode) ?
                    CargoDeliveryCommand.open() : CargoDeliveryCommand.close();
            case "scale":
            case "105":
                return WeightScaleCommand.readWeight();
            case "video":
            case "106":
                // 视频流控制需要额外参数，这里返回基础命令
                return VideoStreamCommand.startStream(1, "");
            case "platform":
            case "107":
                return createPlatformCommand(actionCode);
            case "pusher":
            case "108":
                return "1".equals(actionCode) || "push".equals(actionCode) ?
                    CargoPusherCommand.push() : CargoPusherCommand.retract();
            default:
                throw new IllegalArgumentException("不支持的动作类型: " + action);
        }
    }

    /**
     * 创建升降台命令（根据MQTT协议文档）
     */
    private DroneCommand createPlatformCommand(String actionCode) {
        switch (actionCode) {
            case "1":
            case "reset":
                return LiftPlatformCommand.reset();
            case "2":
            case "rise_pickup":
                return LiftPlatformCommand.riseToPickup();
            case "3":
            case "rise_delivery":
                return LiftPlatformCommand.riseToDelivery();
            case "4":
            case "lower_pickup":
                return LiftPlatformCommand.lowerToPickup();
            case "5":
            case "lower_delivery":
                return LiftPlatformCommand.lowerToDelivery();
            case "6":
            case "rise_landing":
                return LiftPlatformCommand.riseToLanding();
            default:
                throw new IllegalArgumentException("不支持的升降台动作: " + actionCode);
        }
    }
}