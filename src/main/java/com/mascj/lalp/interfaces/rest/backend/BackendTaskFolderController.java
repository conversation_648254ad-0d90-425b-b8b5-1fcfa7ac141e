package com.mascj.lalp.interfaces.rest.backend;

import com.mascj.lalp.application.service.TaskFolderService;
import com.mascj.lalp.common.context.TenantContext;
import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.domain.model.TaskFolder;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.infrastructure.common.api.PageResult;
import com.mascj.lalp.infrastructure.common.security.SecUtil;
import com.mascj.lalp.infrastructure.common.security.UserInfo;
import com.mascj.lalp.interfaces.rest.backend.dto.AddTaskToFolderRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.TaskFavoriteRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.TaskFolderRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.TaskFolderResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * 任务收藏夹控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/backend/task-folders")
@RequiredArgsConstructor

@Tag(name = "任务收藏夹管理", description = "任务收藏夹相关接口")
public class BackendTaskFolderController {

    private final TaskFolderService taskFolderService;

    /**
     * 统一的用户认证和上下文设置
     * Controller层的标准用户认证方法
     */
    private UserInfo getCurrentUser(HttpServletRequest request) {
        try {
            UserInfo userInfo = SecUtil.getUserInfo(request);
            if (userInfo == null) {
                throw new IllegalArgumentException("用户未登录或令牌无效");
            }

            // 设置租户上下文
            Long tenantId = TenantContext.getTenantId();
            if (tenantId == null) {
                tenantId = 1L; // 默认租户ID
                TenantContext.setTenantId(tenantId);
            }

            return userInfo;
        } catch (Exception e) {
            log.warn("用户认证失败: {}", e.getMessage());
            throw new IllegalArgumentException("用户认证失败: " + e.getMessage());
        }
    }

    /**
     * 从UserInfo获取租户ID的辅助方法
     */
    private Long getTenantIdFromUser(UserInfo userInfo) {
        Long tenantId = TenantContext.getTenantId();
        if (tenantId == null) {
            tenantId = 1L;
            TenantContext.setTenantId(tenantId);
            log.debug("设置默认租户ID: {}", tenantId);
        }
        return tenantId;
    }

    /**
     * 统一的参数验证方法
     */
    private void validateTaskId(Long taskId) {
        if (taskId == null || taskId <= 0) {
            throw new IllegalArgumentException("任务ID不能为空且必须大于0");
        }
    }

    /**
     * 统一的参数验证方法
     */
    private void validateFolderId(Long folderId) {
        if (folderId == null || folderId <= 0) {
            throw new IllegalArgumentException("收藏夹ID不能为空且必须大于0");
        }
    }

    /**
     * 创建收藏夹
     * Controller层：参数验证、用户认证、调用Service、返回响应
     *
     * @param httpRequest HTTP请求（用于用户认证）
     * @param request 创建请求
     * @return 创建结果
     */
    @Operation(summary = "创建收藏夹", description = "创建新的任务收藏夹")
    @PostMapping("/add")
    public ApiResult<TaskFolderResponse> createFolder(
            HttpServletRequest httpRequest,
            @RequestBody TaskFolderRequest request) {

        try {
            // 1. 参数验证
            if (request == null) {
                return ApiResult.badRequest("请求参数不能为空");
            }
            if (request.getFolderName() == null || request.getFolderName().trim().isEmpty()) {
                return ApiResult.badRequest("收藏夹名称不能为空");
            }

            // 2. 用户认证
            UserInfo currentUser = getCurrentUser(httpRequest);
            Long tenantId = getTenantIdFromUser(currentUser);

            log.info("创建收藏夹请求: folderName={}, userId={}",
                    request.getFolderName(), currentUser.getAccountId());

            // 3. 调用Service处理业务逻辑
            TaskFolder folder = taskFolderService.createFolder(
                    request, currentUser.getAccountId(), currentUser.getName(), tenantId);
            TaskFolderResponse response = TaskFolderResponse.of(folder);

            log.info("创建收藏夹成功: folderId={}, folderName={}",
                    response.getId(), response.getFolderName());

            // 4. 返回响应
            return ApiResult.success(response);

        } catch (IllegalArgumentException e) {
            log.warn("创建收藏夹参数错误: {}", e.getMessage());
            return ApiResult.badRequest(e.getMessage());
        } catch (Exception e) {
            log.error("创建收藏夹系统异常", e);
            return ApiResult.serverError("创建收藏夹失败: " + e.getMessage());
        }
    }

    /**
     * 更新收藏夹
     * Controller层：参数验证、用户认证、调用Service、返回响应
     *
     * @param id 收藏夹ID
     * @param request 更新请求
     * @param httpRequest HTTP请求（用于用户认证）
     * @return 更新结果
     */
    @Operation(summary = "更新收藏夹", description = "更新指定收藏夹的信息")
    @PutMapping("/update/{id}")
    public ApiResult<TaskFolderResponse> updateFolder(
            @Parameter(description = "收藏夹ID", required = true) @PathVariable Long id,
            @RequestBody TaskFolderRequest request,
            HttpServletRequest httpRequest) {

        try {
            // 1. 参数验证
            validateFolderId(id);
            if (request == null) {
                return ApiResult.badRequest("请求参数不能为空");
            }
            if (request.getFolderName() == null || request.getFolderName().trim().isEmpty()) {
                return ApiResult.badRequest("收藏夹名称不能为空");
            }

            // 2. 用户认证
            UserInfo currentUser = getCurrentUser(httpRequest);
            Long tenantId = getTenantIdFromUser(currentUser);

            log.info("更新收藏夹请求: id={}, folderName={}, userId={}",
                    id, request.getFolderName(), currentUser.getAccountId());

            // 3. 调用Service处理业务逻辑
            TaskFolder folder = taskFolderService.updateFolder(
                    id, request, currentUser.getAccountId(), tenantId);
            TaskFolderResponse response = TaskFolderResponse.of(folder);

            log.info("更新收藏夹成功: id={}, folderName={}", id, response.getFolderName());

            // 4. 返回响应
            return ApiResult.success(response);

        } catch (IllegalArgumentException e) {
            log.warn("更新收藏夹参数错误: id={}, error={}", id, e.getMessage());
            return ApiResult.badRequest(e.getMessage());
        } catch (Exception e) {
            log.error("更新收藏夹系统异常: id={}", id, e);
            return ApiResult.serverError("更新收藏夹失败: " + e.getMessage());
        }
    }

    /**
     * 删除收藏夹
     * Controller层：参数验证、用户认证、调用Service、返回响应
     *
     * @param id 收藏夹ID
     * @param httpRequest HTTP请求（用于用户认证）
     * @return 删除结果
     */
    @Operation(summary = "删除收藏夹", description = "删除指定的收藏夹及其所有收藏关系")
    @DeleteMapping("/delete/{id}")
    public ApiResult<Void> deleteFolder(
            @Parameter(description = "收藏夹ID", required = true) @PathVariable Long id,
            HttpServletRequest httpRequest) {

        try {
            // 1. 参数验证
            validateFolderId(id);

            // 2. 用户认证
            UserInfo currentUser = getCurrentUser(httpRequest);
            Long tenantId = getTenantIdFromUser(currentUser);

            log.info("删除收藏夹请求: id={}, userId={}", id, currentUser.getAccountId());

            // 3. 调用Service处理业务逻辑
            taskFolderService.deleteFolder(id, currentUser.getAccountId(), tenantId);

            log.info("删除收藏夹成功: id={}", id);

            // 4. 返回响应
            return ApiResult.success("删除成功", null);

        } catch (IllegalArgumentException e) {
            log.warn("删除收藏夹参数错误: id={}, error={}", id, e.getMessage());
            return ApiResult.badRequest(e.getMessage());
        } catch (Exception e) {
            log.error("删除收藏夹系统异常: id={}", id, e);
            return ApiResult.serverError("删除收藏夹失败: " + e.getMessage());
        }
    }

    /**
     * 查询收藏夹列表
     * Controller层：参数验证、用户认证、调用Service、返回响应
     *
     * @param httpRequest HTTP请求（用于用户认证）
     * @param includeTaskCount 是否包含任务数量统计
     * @return 收藏夹列表
     */
    @Operation(summary = "查询收藏夹列表", description = "获取当前用户的所有收藏夹")
    @GetMapping("/list")
    public ApiResult<List<TaskFolderResponse>> listFolders(
            HttpServletRequest httpRequest,
            @Parameter(description = "是否包含任务数量统计") @RequestParam(defaultValue = "false") boolean includeTaskCount) {

        try {
            // 1. 用户认证
            UserInfo currentUser = getCurrentUser(httpRequest);
            Long tenantId = getTenantIdFromUser(currentUser);

            log.info("查询收藏夹列表请求: userId={}, includeTaskCount={}",
                    currentUser.getAccountId(), includeTaskCount);

            // 2. 调用Service处理业务逻辑
            List<TaskFolder> folders = taskFolderService.listUserFolders(
                    currentUser.getAccountId(), tenantId);
            List<TaskFolderResponse> responses = folders.stream()
                    .map(TaskFolderResponse::of)
                    .collect(Collectors.toList());

            log.info("查询收藏夹列表成功: userId={}, count={}",
                    currentUser.getAccountId(), responses.size());

            // 3. 返回响应
            return ApiResult.success(responses);

        } catch (IllegalArgumentException e) {
            log.warn("查询收藏夹列表参数错误: {}", e.getMessage());
            return ApiResult.badRequest(e.getMessage());
        } catch (Exception e) {
            log.error("查询收藏夹列表系统异常", e);
            return ApiResult.serverError("查询收藏夹列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询可作为父级的收藏夹列表
     * Controller层：参数验证、用户认证、调用Service、返回响应
     *
     * @param excludeId 排除的收藏夹ID（可选，用于编辑时避免循环引用）
     * @param httpRequest HTTP请求（用于用户认证）
     * @return 可选父级收藏夹列表
     */
    @Operation(summary = "查询可作为父级的收藏夹列表", description = "获取可以作为父级的收藏夹列表，用于层级管理")
    @GetMapping("/list-available-parents")
    public ApiResult<List<TaskFolderResponse>> listAvailableParents(
            @Parameter(description = "排除的收藏夹ID", example = "1") @RequestParam(required = false) Long excludeId,
            HttpServletRequest httpRequest) {

        try {
            // 1. 参数验证（excludeId可选，无需验证）

            // 2. 用户认证
            UserInfo currentUser = getCurrentUser(httpRequest);
            Long tenantId = getTenantIdFromUser(currentUser);

            log.info("查询可选父级收藏夹请求: userId={}, excludeId={}",
                    currentUser.getAccountId(), excludeId);

            // 3. 调用Service处理业务逻辑
            List<TaskFolder> folders = taskFolderService.listAvailableParents(tenantId, excludeId);
            List<TaskFolderResponse> responses = folders.stream()
                    .map(TaskFolderResponse::of)
                    .collect(Collectors.toList());

            log.info("查询可选父级收藏夹成功: userId={}, count={}",
                    currentUser.getAccountId(), responses.size());

            // 4. 返回响应
            return ApiResult.success(responses);

        } catch (IllegalArgumentException e) {
            log.warn("查询可选父级收藏夹参数错误: excludeId={}, error={}", excludeId, e.getMessage());
            return ApiResult.badRequest(e.getMessage());
        } catch (Exception e) {
            log.error("查询可选父级收藏夹系统异常: excludeId={}", excludeId, e);
            return ApiResult.serverError("查询可选父级收藏夹失败: " + e.getMessage());
        }
    }

    /**
     * 查询指定父级下的子收藏夹列表
     *
     * @param parentId 父级收藏夹ID
     * @return 子收藏夹列表
     */
    @Operation(summary = "查询指定父级下的子收藏夹列表")
    @GetMapping("/list-children/{parentId}")
    public ApiResult<List<TaskFolderResponse>> listChildFolders(
            @Parameter(description = "父级收藏夹ID", required = true) @PathVariable Long parentId,
            HttpServletRequest httpRequest) {

        log.info("查询子收藏夹列表: parentId={}", parentId);

        try {
            // 从JWT获取当前用户信息（获取租户ID）
            UserInfo currentUser = getCurrentUser(httpRequest);
            Long tenantId = getTenantIdFromUser(currentUser);

            List<TaskFolder> folders = taskFolderService.listChildFolders(parentId, tenantId);
            List<TaskFolderResponse> responses = folders.stream()
                    .map(TaskFolderResponse::of)
                    .collect(Collectors.toList());

            log.info("查询子收藏夹列表成功: parentId={}, count={}", parentId, responses.size());
            return ApiResult.success(responses);

        } catch (Exception e) {
            log.error("查询子收藏夹列表异常", e);
            return ApiResult.serverError("查询子收藏夹列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取收藏夹详情
     *
     * @param id 收藏夹ID
     * @return 收藏夹详情
     */
    @Operation(summary = "获取收藏夹详情")
    @GetMapping("/detail/{id}")
    public ApiResult<TaskFolderResponse> getFolderDetail(
            @Parameter(description = "收藏夹ID", required = true) @PathVariable Long id,
            HttpServletRequest httpRequest) {

        log.info("获取收藏夹详情: id={}", id);

        try {
            // 从JWT获取当前用户信息（获取租户ID）
            UserInfo currentUser = getCurrentUser(httpRequest);
            Long tenantId = getTenantIdFromUser(currentUser);

            // 获取收藏夹详情（不需要用户验证，所有人都能查看）
            TaskFolder folder = taskFolderService.getFolderDetail(id, currentUser.getAccountId(), tenantId);
            if (folder == null) {
                log.warn("收藏夹不存在: id={}", id);
                return ApiResult.notFound("收藏夹不存在");
            }

            TaskFolderResponse response = TaskFolderResponse.of(folder);
            log.info("获取收藏夹详情成功: id={}, folderName={}", folder.getId(), folder.getFolderName());
            return ApiResult.success(response);

        } catch (Exception e) {
            log.error("获取收藏夹详情异常", e);
            return ApiResult.serverError("获取收藏夹详情失败: " + e.getMessage());
        }
    }

    /**
     * 收藏任务到收藏夹
     * 符合RESTful规范的POST接口，使用JSON请求体传递参数
     * 专注于收藏功能，支持指定收藏夹收藏和快速收藏到默认收藏夹
     * 注意：此接口只执行收藏操作，不会取消已有收藏
     *
     * @param httpRequest HTTP请求（用于获取用户信息）
     * @param request 收藏请求参数
     * @return 收藏结果
     */
    @Operation(summary = "收藏任务到收藏夹",
               description = "符合RESTful规范的收藏接口，使用JSON请求体传递参数。支持指定收藏夹收藏和快速收藏到默认收藏夹。")
    @PostMapping("/favorites")
    public ApiResult<Map<String, Object>> addTaskToFolder(
            HttpServletRequest httpRequest,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "收藏请求参数",
                required = true,
                content = @io.swagger.v3.oas.annotations.media.Content(
                    mediaType = "application/json",
                    schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = AddTaskToFolderRequest.class)
                )
            )
            @RequestBody @Valid AddTaskToFolderRequest request) {

        try {
            // 1. 参数验证（@Valid注解会自动验证）
            log.info("收藏任务请求: {}", request);

            // 2. 获取当前用户信息
            UserInfo currentUser = getCurrentUser(httpRequest);
            Long tenantId = getTenantIdFromUser(currentUser);

            log.info("收藏任务请求: taskId={}, folderId={}, userId={}, quickFavorite={}",
                    request.getTaskId(), request.getFolderId(), currentUser.getAccountId(), request.isQuickFavorite());

            Map<String, Object> result;

            if (request.isQuickFavorite()) {
                // 快速收藏到默认收藏夹
                result = addTaskToDefaultFolder(request.getTaskId(), currentUser, tenantId);
            } else {
                // 收藏到指定收藏夹
                result = addTaskToSpecificFolder(request.getTaskId(), request.getFolderId(), currentUser, tenantId);
            }

            log.info("收藏任务成功: taskId={}, folderId={}", request.getTaskId(), result.get("folderId"));
            return ApiResult.success(result);

        } catch (IllegalArgumentException e) {
            log.warn("收藏任务参数错误: request={}, error={}", request, e.getMessage());
            return ApiResult.badRequest(e.getMessage());
        } catch (Exception e) {
            log.error("收藏任务系统异常: request={}", request, e);
            return ApiResult.serverError("收藏失败: " + e.getMessage());
        }
    }

    /**
     * 收藏任务到指定收藏夹
     */
    private Map<String, Object> addTaskToSpecificFolder(Long taskId, Long folderId,
                                                        UserInfo currentUser, Long tenantId) {

        log.info("收藏任务到指定收藏夹: taskId={}, folderId={}, userId={}",
                taskId, folderId, currentUser.getAccountId());

        // 调用Service层处理业务逻辑（包括权限检查、重复检查等）
        Map<String, Object> result = taskFolderService.addTaskToFolder(
                taskId, folderId, currentUser.getAccountId(), currentUser.getName(), tenantId);

        log.info("收藏任务到指定收藏夹成功: taskId={}, folderId={}", taskId, folderId);
        return result;
    }

    /**
     * 快速收藏任务到默认收藏夹
     */
    private Map<String, Object> addTaskToDefaultFolder(Long taskId, UserInfo currentUser, Long tenantId) {
        // 调用Service层的快速收藏方法
        return taskFolderService.quickFavoriteTask(taskId,
                currentUser.getAccountId(), currentUser.getName(), tenantId);
    }

    /**
     * 智能收藏/取消收藏任务
     * 专注于智能切换功能：如果任务已收藏，则取消收藏；如果未收藏，则收藏到指定收藏夹
     * 注意：收藏时必须指定folderId，不支持快速收藏到默认收藏夹
     *
     * @param request HTTP请求（用于获取用户信息）
     * @param taskId 任务ID
     * @param folderId 收藏夹ID（收藏时必填，取消收藏时忽略）
     * @return 操作结果
     */
    @Operation(summary = "智能收藏/取消收藏",
               description = "专注于智能切换功能：根据任务当前收藏状态执行相反操作。收藏时必须指定folderId，不支持快速收藏。")
    @PostMapping("/smart-favorite/{taskId}")
    public ApiResult<Map<String, Object>> smartFavoriteTask(
            HttpServletRequest request,
            @Parameter(description = "任务ID", required = true) @PathVariable Long taskId,
            @Parameter(description = "收藏夹ID（收藏时必填，取消收藏时忽略）") @RequestParam(required = false) Long folderId) {

        try {
            // 1. 参数验证
            if (taskId == null || taskId <= 0) {
                return ApiResult.badRequest("任务ID不能为空且必须大于0");
            }

            // 2. 获取当前用户信息
            UserInfo currentUser = getCurrentUser(request);
            Long tenantId = getTenantIdFromUser(currentUser);

            log.info("智能收藏切换请求: taskId={}, folderId={}, userId={}",
                    taskId, folderId, currentUser.getAccountId());

            // 3. 检查任务当前收藏状态
            List<com.mascj.lalp.domain.model.TaskFavorite> existingFavorites =
                    taskFolderService.getTaskFavorites(taskId, currentUser.getAccountId(), tenantId);

            Map<String, Object> result;

            if (!existingFavorites.isEmpty()) {
                // 任务已收藏，执行取消收藏操作
                result = executeUnfavoriteOperation(taskId, existingFavorites, currentUser, tenantId);
            } else {
                // 任务未收藏，执行收藏操作（必须指定folderId）
                if (folderId == null || folderId <= 0) {
                    return ApiResult.badRequest("收藏任务时必须指定有效的收藏夹ID");
                }
                result = executeFavoriteOperation(taskId, folderId, currentUser, tenantId);
            }

            log.info("智能收藏切换完成: taskId={}, action={}", taskId, result.get("action"));
            return ApiResult.success(result);

        } catch (IllegalArgumentException e) {
            log.warn("智能收藏切换参数错误: taskId={}, error={}", taskId, e.getMessage());
            return ApiResult.badRequest(e.getMessage());
        } catch (Exception e) {
            log.error("智能收藏切换系统异常: taskId={}", taskId, e);
            return ApiResult.serverError("操作失败: " + e.getMessage());
        }
    }

    /**
     * 执行取消收藏操作
     */
    private Map<String, Object> executeUnfavoriteOperation(Long taskId,
                                                          List<com.mascj.lalp.domain.model.TaskFavorite> existingFavorites,
                                                          UserInfo currentUser, Long tenantId) {

        log.info("执行取消收藏: taskId={}, favoriteCount={}", taskId, existingFavorites.size());

        // 从所有收藏夹中移除该任务
        int removedCount = 0;
        for (com.mascj.lalp.domain.model.TaskFavorite favorite : existingFavorites) {
            try {
                taskFolderService.removeTaskFromFolder(favorite.getFolderId(), taskId,
                        currentUser.getAccountId(), tenantId);
                removedCount++;
            } catch (Exception e) {
                log.warn("从收藏夹移除任务失败: folderId={}, taskId={}, error={}",
                        favorite.getFolderId(), taskId, e.getMessage());
            }
        }

        // 构建响应
        Map<String, Object> result = new HashMap<>();
        result.put("action", "unfavorite");
        result.put("favorited", false);
        result.put("message", "取消收藏成功");
        result.put("taskId", taskId);
        result.put("removedCount", removedCount);
        result.put("originalFavoriteCount", existingFavorites.size());
        result.put("timestamp", System.currentTimeMillis());

        return result;
    }

    /**
     * 执行收藏操作
     */
    private Map<String, Object> executeFavoriteOperation(Long taskId, Long folderId,
                                                        UserInfo currentUser, Long tenantId) {

        log.info("执行收藏: taskId={}, folderId={}", taskId, folderId);

        // 验证收藏夹是否存在（跨租户访问）
        TaskFolder targetFolder = taskFolderService.getFolderByIdIgnoreTenant(folderId);
        if (targetFolder == null) {
            throw new IllegalArgumentException("指定的收藏夹不存在: " + folderId);
        }

        // 执行收藏操作
        TaskFavoriteRequest favoriteRequest = new TaskFavoriteRequest();
        favoriteRequest.setTaskId(taskId);
        favoriteRequest.setFolderId(folderId);

        taskFolderService.addTaskToFolderCrossTenant(favoriteRequest,
                currentUser.getAccountId(), currentUser.getName(), tenantId);

        // 构建响应
        Map<String, Object> result = new HashMap<>();
        result.put("action", "favorite");
        result.put("favorited", true);
        result.put("message", "收藏成功");
        result.put("taskId", taskId);
        result.put("folderId", folderId);
        result.put("folderName", targetFolder.getFolderName());
        result.put("timestamp", System.currentTimeMillis());

        return result;
    }
    /**
     * 从收藏夹移除任务
     *
     * @param folderId 收藏夹ID
     * @param taskId 任务ID
     * @return 移除结果
     */
    @Operation(summary = "从收藏夹移除任务")
    @DeleteMapping("/remove-task/{folderId}/{taskId}")
    public ApiResult<Void> removeTaskFromFolder(
            @Parameter(description = "收藏夹ID", required = true) @PathVariable Long folderId,
            @Parameter(description = "任务ID", required = true) @PathVariable Long taskId,
            HttpServletRequest httpRequest) {

        log.info("移除任务收藏: folderId={}, taskId={}", folderId, taskId);

        try {
            // 从JWT获取当前用户信息（移除任务时需要权限验证）
            UserInfo currentUser = getCurrentUser(httpRequest);
            Long tenantId = getTenantIdFromUser(currentUser);

            taskFolderService.removeTaskFromFolder(folderId, taskId, currentUser.getAccountId(), tenantId);

            log.info("任务收藏移除成功: folderId={}, taskId={}", folderId, taskId);
            return ApiResult.success("移除成功", null);

        } catch (Exception e) {
            log.error("移除任务收藏异常", e);
            return ApiResult.serverError("移除任务收藏失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询收藏夹中的任务
     *
     * @param folderId 收藏夹ID
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 任务分页结果
     */
    @Operation(summary = "分页查询收藏夹中的任务")
    @GetMapping("/list-tasks/{folderId}")
    public ApiResult<PageResult<DeliveryTask>> listFolderTasks(
            @Parameter(description = "收藏夹ID", required = true) @PathVariable Long folderId,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "页大小", example = "10") @RequestParam(defaultValue = "10") int pageSize,
            HttpServletRequest httpRequest) {

        log.info("查询收藏夹任务: folderId={}, pageNum={}, pageSize={}", folderId, pageNum, pageSize);

        try {
            // 从JWT获取当前用户信息（获取租户ID）
            UserInfo currentUser = getCurrentUser(httpRequest);
            Long tenantId = getTenantIdFromUser(currentUser);

            // 查询收藏夹任务（不需要用户验证，所有人都能查看）
            PageResult<DeliveryTask> pageResult = taskFolderService.listFolderTasks(
                    folderId, pageNum, pageSize, currentUser.getAccountId(), tenantId);

            log.info("查询收藏夹任务成功: folderId={}, total={}", folderId, pageResult.getTotal());
            return ApiResult.success(pageResult);

        } catch (IllegalArgumentException e) {
            log.warn("查询收藏夹任务失败: {}", e.getMessage());
            return ApiResult.badRequest(e.getMessage());
        } catch (Exception e) {
            log.error("查询收藏夹任务异常", e);
            return ApiResult.serverError("查询收藏夹任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询任务收藏状态
     * 支持单个任务查询（GET）和批量任务查询（POST）
     *
     * @param taskId 任务ID（单个查询时使用）
     * @return 收藏状态
     */
    @Operation(summary = "查询单个任务收藏状态", description = "查询指定任务的收藏状态和收藏夹信息")
    @GetMapping("/favorite-status/{taskId}")
    public ApiResult<Map<String, Object>> getTaskFavoriteStatus(
            @RequestHeader(SecUtil.LIANGMA_TOKEN) String token,
            @Parameter(description = "任务ID", required = true) @PathVariable Long taskId) {

        log.info("查询任务收藏状态: taskId={}", taskId);

        Map<String, Object> result = taskFolderService.getTaskFavoriteStatusWithAuth(token, taskId);
        return ApiResult.success(result);
    }

    /**
     * 批量查询任务收藏状态
     *
     * @param taskIds 任务ID列表
     * @return 收藏状态映射
     */
    @Operation(summary = "批量查询任务收藏状态", description = "批量查询多个任务的收藏状态")
    @PostMapping("/favorite-status")
    public ApiResult<Map<Long, Boolean>> getBatchTaskFavoriteStatus(
            @RequestHeader(SecUtil.LIANGMA_TOKEN) String token,
            @RequestBody List<Long> taskIds) {

        log.info("批量查询任务收藏状态: taskIds={}", taskIds);

        Map<Long, Boolean> result = taskFolderService.getBatchTaskFavoriteStatusWithAuth(token, taskIds);
        return ApiResult.success(result);
    }

}
