package com.mascj.lalp.interfaces.rest.backend.dto;

import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.domain.model.DeliveryTaskStatus;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 配送计划响应DTO
 */
@Data
@Builder
public class DeliveryPlanResponse {
    /**
     * 计划ID，用于唯一标识一个计划
     */
    private Long id;
    
    /**
     * 计划编号，用于外部参考和查询
     */
    private String planNo;

    /**
     * 订单号，关联的订单编号
     */
    private String orderNo;
    
    /**
     * 计划名称，描述该计划的主题或目的
     */
    private String planName;
    
    /**
     * 出发仓库代码，标识货物出发的仓库
     */
    private String fromWarehouseCode;
    
    /**
     * 出发仓库名称，便于人类阅读和理解
     */
    private String fromWarehouseName;
    
    /**
     * 收货仓库代码，标识货物目的仓库
     */
    private String recipientWarehouseCode;
    
    /**
     * 收货仓库名称，便于人类阅读和理解
     */
    private String recipientWarehouseName;
    
    /**
     * 发货人名称，用于联系和参考
     */
    private String senderName;
    
    /**
     * 发货人电话，用于联系发货人
     */
    private String senderPhone;
    
    /**
     * 收货人名称，用于联系和参考
     */
    private String recipientName;
    
    /**
     * 收货人电话，用于联系收货人
     */
    private String recipientPhone;
    
    /**
     * 货物类型代码，分类货物的种类
     */
    private String cargoTypeCode;
    
    /**
     * 货物类型，对货物类型代码的描述
     */
    private String cargoType;
    
    /**
     * 货物内容，描述货物的具体信息
     */
    private String cargoContent;
    
    /**
     * 货物重量，以千克为单位
     */
    private Double cargoWeight;
    
    /**
     * 计划状态，描述计划当前所处的阶段或状况
     */
    private String status;
    
    /**
     * 计划时间，记录计划的安排时间
     */
    private LocalDateTime planTime;
    
    /**
     * 创建时间，记录计划的创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 返回时间
     */
    private LocalDateTime returnTime;
    
    /**
     * 送货时间
     */
    private LocalDateTime deliveryTime;
    
    /**
     * 总飞行时间
     */
    private int totalFlightTime;

    /**
     * 起飞时间
     */
    private LocalDateTime departureTime;
    /**
     * 送达时间
     */
    private LocalDateTime arrivalTime;
    /**
     * 配送距离
     */
    private Double deliveryDistance;
    /**
     *总飞行距离
     */
    private Double flightDistance;
    //无人机编号
    private String droneId;

    /**
     * 是否已收藏
     */
    private Boolean favorited;

    /**
     * 创建DeliveryPlanResponse实例
     * 
     * @param task 配送任务对象，包含计划的基本信息
     * @return 返回构建好的DeliveryPlanResponse对象
     *          包含了从配送任务中提取的各种属性信息
     */
    public static DeliveryPlanResponse of(DeliveryTask task) {
        // 使用 Lombok 的 builder 构建 DeliveryPlanResponse 实例
        DeliveryPlanResponse response = DeliveryPlanResponse.builder()
                // 设置计划ID，来源于配送任务的ID
                .id(task.getId())
                //无人机编号
                .droneId(task.getDroneId())
                // 设置计划编号，格式为PLAN-+任务ID
                .planNo("PLAN-" + task.getId())
                // 设置订单号，来源于配送任务的订单号
                .orderNo(task.getOrderNo())
                // 设置计划名称，来源于配送任务中的计划名称
                .planName(task.getPlanName())
                // 设置出发仓库代码，来源于配送任务的出发点
                .fromWarehouseCode(task.getDeparturePoint())
                // 设置出发仓库名称，来源于配送任务的发货仓库名称
                .fromWarehouseName(task.getDeparturePointName() != null ? task.getDeparturePointName() : "")
                // 设置收货仓库代码，来源于配送任务的到达点
                .recipientWarehouseCode(task.getArrivalPoint())
                // 设置收货仓库名称，来源于配送任务的收货仓库名称
                .recipientWarehouseName(task.getArrivalPointName() != null ? task.getArrivalPointName() : "")
                // 设置发货人名称，来源于配送任务的创建者名称
                .senderName(task.getCreatorName())
                // 设置发货人电话，来源于配送任务的创建者电话
                .senderPhone(task.getCreatorPhone())
                // 设置收货人名称，来源于配送任务的接收者名称
                .recipientName(task.getReceiverName())
                // 设置收货人电话，来源于配送任务的接收者电话
                .recipientPhone(task.getReceiverPhone())
                // 设置货物类型代码，来源于配送任务的货物类型代码
                .cargoTypeCode(task.getCargoTypeCode())
                // 设置货物类型，来源于配送任务的货物类型名称
                .cargoType(task.getCargoType())
                // 设置货物内容，来源于配送任务的货物内容
                .cargoContent(task.getCargoContent())
                // 设置货物重量，来源于配送任务的货物重量
                .cargoWeight(task.getCargoWeight())
                // 设置返回时间，暂时使用收货时间
                .returnTime(task.getReceivedTime())
                // 设置送货时间，来源于配送任务的送货时间
                .deliveryTime(task.getDeliveryTime())
                // 设置总飞行时间，来源于配送任务的总飞行时长
                .totalFlightTime(task.getTotalFlightTime())
                // 设置起飞时间，来源于配送任务的起飞时间
                .departureTime(task.getDepartureTime())
                // 设置送达时间，来源于配送任务的送达时间
                .arrivalTime(task.getArrivalTime())
                // 设置配送距离，来源于配送任务的配送距离
                .deliveryDistance(task.getDeliveryDistance())
                // 设置计划状态，返回枚举名称
                .status(task.getStatus() != null ? task.getStatus().name() : null)
                // 设置计划时间，使用配送任务的创建时间作为计划时间
                .planTime(task.getCreateTime())
                // 设置创建时间，来源于配送任务的创建时间
                .createTime(task.getCreateTime())
                //设置总飞行距离，来源于配送任务的总飞行距离
                .flightDistance(task.getFlightDistance())
                // 默认未收藏
                .favorited(false)
                // 构建最终的DeliveryPlanResponse对象
                .build();
        // 返回构建完成的响应对象
        return response;
    }

    /**
     * 创建DeliveryPlanResponse实例（带收藏状态）
     *
     * @param task 配送任务对象，包含计划的基本信息
     * @param favorited 是否已收藏
     * @return 返回构建好的DeliveryPlanResponse对象
     */
    public static DeliveryPlanResponse of(DeliveryTask task, Boolean favorited) {
        DeliveryPlanResponse response = of(task);
        if (response != null) {
            response.setFavorited(favorited != null ? favorited : false);
        }
        return response;
    }

    /**
     * 设置收藏状态
     * @param favorited 是否已收藏
     */
    public void setFavoriteStatus(Boolean favorited) {
        this.favorited = favorited != null ? favorited : false;
    }



}
