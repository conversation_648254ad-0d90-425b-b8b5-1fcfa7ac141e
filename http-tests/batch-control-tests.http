### 无人机货仓批量控制HTTP测试
### 基础URL配置
@baseUrl = http://localhost:8080
@deviceId = 49004A001151323532363931

### 1. 简单批量控制 - 打开舱盖然后关闭
POST {{baseUrl}}/api/drone/{{deviceId}}/batch
Content-Type: application/json

{
  "action": 111,
  "data": {
    "commands": [
      {
        "action": 101,
        "action_code": 2,
        "delay": 0,
        "params": null
      }
    ]
  }
}
###
POST http://localhost:8080/api/drone/{{deviceId}}/cover/open
Content-Type: application/json
###
POST http://localhost:8080/api/drone/{{deviceId}}/cover/close
Content-Type: application/x-www-form-urlencoded


### 2. 取货流程 - 完整的取货操作序列
POST {{baseUrl}}/api/drone/{{deviceId}}/batch
Content-Type: application/json

{
  "action": 111,
  "data": {
    "commands": [
      {
        "action": 101,
        "action_code": 1,
        "delay": 0,
        "params": null
      },
      {
        "action": 102,
        "action_code": 1,
        "delay": 1000,
        "params": null
      },
      {
        "action": 107,
        "action_code": 2,
        "delay": 2000,
        "params": null
      },
      {
        "action": 103,
        "action_code": 1,
        "delay": 1500,
        "params": null
      },
      {
        "action": 105,
        "action_code": 1,
        "delay": 3000,
        "params": null
      },
      {
        "action": 103,
        "action_code": 2,
        "delay": 2000,
        "params": null
      },
      {
        "action": 107,
        "action_code": 1,
        "delay": 1000,
        "params": null
      },
      {
        "action": 102,
        "action_code": 2,
        "delay": 1500,
        "params": null
      },
      {
        "action": 101,
        "action_code": 2,
        "delay": 2000,
        "params": null
      }
    ]
  }
}

### 3. 送货流程 - 完整的送货操作序列
POST {{baseUrl}}/api/drone/{{deviceId}}/batch
Content-Type: application/json

{
  "action": 111,
  "data": {
    "commands": [
      {
        "action": 101,
        "action_code": 1,
        "delay": 0,
        "params": null
      },
      {
        "action": 107,
        "action_code": 3,
        "delay": 2000,
        "params": null
      },
      {
        "action": 104,
        "action_code": 1,
        "delay": 1500,
        "params": null
      },
      {
        "action": 108,
        "action_code": 1,
        "delay": 2000,
        "params": null
      },
      {
        "action": 108,
        "action_code": 2,
        "delay": 3000,
        "params": null
      },
      {
        "action": 104,
        "action_code": 2,
        "delay": 1000,
        "params": null
      },
      {
        "action": 107,
        "action_code": 1,
        "delay": 1500,
        "params": null
      },
      {
        "action": 101,
        "action_code": 2,
        "delay": 2000,
        "params": null
      }
    ]
  }
}

### 4. 复杂操作 - 取货+送货完整流程
POST {{baseUrl}}/api/drone/{{deviceId}}/batch
Content-Type: application/json

{
  "action": 111,
  "data": {
    "commands": [
      {
        "action": 101,
        "action_code": 1,
        "delay": 0,
        "params": null
      },
      {
        "action": 102,
        "action_code": 1,
        "delay": 1000,
        "params": null
      },
      {
        "action": 107,
        "action_code": 2,
        "delay": 2000,
        "params": null
      },
      {
        "action": 103,
        "action_code": 1,
        "delay": 1500,
        "params": null
      },
      {
        "action": 105,
        "action_code": 1,
        "delay": 3000,
        "params": null
      },
      {
        "action": 103,
        "action_code": 2,
        "delay": 2000,
        "params": null
      },
      {
        "action": 107,
        "action_code": 3,
        "delay": 2000,
        "params": null
      },
      {
        "action": 104,
        "action_code": 1,
        "delay": 1500,
        "params": null
      },
      {
        "action": 108,
        "action_code": 1,
        "delay": 2000,
        "params": null
      },
      {
        "action": 108,
        "action_code": 2,
        "delay": 3000,
        "params": null
      },
      {
        "action": 104,
        "action_code": 2,
        "delay": 1000,
        "params": null
      },
      {
        "action": 107,
        "action_code": 1,
        "delay": 1500,
        "params": null
      },
      {
        "action": 102,
        "action_code": 2,
        "delay": 1000,
        "params": null
      },
      {
        "action": 101,
        "action_code": 2,
        "delay": 2000,
        "params": null
      }
    ]
  }
}

### 5. 快速测试 - 只读取重量
POST {{baseUrl}}/api/drone/{{deviceId}}/batch
Content-Type: application/json

{
  "action": 111,
  "data": {
    "commands": [
      {
        "action": 105,
        "action_code": 1,
        "delay": 0,
        "params": null
      }
    ]
  }
}

###
POST http://localhost:8080/api/drone/{{deviceId}}/pickup/open


###
POST http://localhost:8080/api/drone/{{deviceId}}/pickup/close?
    withStatus=false



###
POST http://localhost:8080/api/drone//pickup/close?
    withStatus=false



###
 POST http://localhost:8080/api/drone/{{deviceId}}/delivery/open?withStatus=true

###
POST http://localhost:8080/api/drone/{{deviceId}}/delivery/close?withStatus=true


### 6. 查看设备状态
GET {{baseUrl}}/api/drone/{{deviceId}}/status

### 7. 紧急停止 - 关闭所有开口
POST {{baseUrl}}/api/drone/{{deviceId}}/batch
Content-Type: application/json

{
  "action": 111,
  "data": {
    "commands": [
      {
        "action": 103,
        "action_code": 2,
        "delay": 0,
        "params": null
      },
      {
        "action": 104,
        "action_code": 2,
        "delay": 500,
        "params": null
      },
      {
        "action": 101,
        "action_code": 2,
        "delay": 1000,
        "params": null
      },
      {
        "action": 107,
        "action_code": 1,
        "delay": 1500,
        "params": null
      },
      {
        "action": 102,
        "action_code": 2,
        "delay": 2000,
        "params": null
      }
    ]
  }
}
